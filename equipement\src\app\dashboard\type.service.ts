import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Injectable, Type } from '@angular/core';
import { TypeEqui } from './TypeEqui';
import { Observable, of } from 'rxjs';
import { Marque } from '../marque/Marque';
import { Model } from '../model/Model';
import { Equip } from '../equipement/equip';
import { Fournisseur } from '../fournisseur/Fournisseur';
import { AffectationEquipement } from '../utilisateur-equipement/AffectationEquipement';
import { Affectation } from '../affecta/Affectation';
import { Historique } from '../equipement/Historique';

@Injectable({
  providedIn: 'root'
})
export class TypeService {

private baseURL="http://localhost:8085/equi";

  constructor(private httpClient:HttpClient) { }

  // Helper method to get authentication headers
  private getAuthHeaders(): HttpHeaders {
    const token = sessionStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  addType(TypeEqui: TypeEqui): Observable<TypeEqui> {
    return this.httpClient.post<TypeEqui>(`${this.baseURL}/addType`, TypeEqui);
  }
    getTypes(): Observable<TypeEqui[]> {
    return this.httpClient.get<TypeEqui[]>(`${this.baseURL}/getTypes`);
  }
  getAllTypes(): Observable<TypeEqui[]> {
    return this.httpClient.get<TypeEqui[]>(`${this.baseURL}/getall`, {
      headers: this.getAuthHeaders()
    });
  }
  getAllMarques(): Observable<Marque[]> {
    return this.httpClient.get<Marque[]>(`${this.baseURL}/getallMarque`, {
      headers: this.getAuthHeaders()
    });
  }
  getAllModel(): Observable<Model[]> {
    return this.httpClient.get<Model[]>(`${this.baseURL}/getModels`, {
      headers: this.getAuthHeaders()
    });
  }
  addMarque(marque:Marque ): Observable<Marque> {
    return this.httpClient.post<Marque>(`${this.baseURL}/addMarque`,marque);
  }
deleteType(id: number): Observable<void> {
 
  return this.httpClient.delete<void>(this.baseURL + '/deleteType' + '/' + id);
}
updateType(typeEqui:TypeEqui): Observable<Object> {
  return this.httpClient.put<Object>(this.baseURL + '/updateType',typeEqui);
}
deleteMarque(id: number): Observable<void> {
 
  return this.httpClient.delete<void>(this.baseURL + '/deleteMarque' + '/' + id);
}
updateMarque(marque:any): Observable<Object> {
  return this.httpClient.put<Object>(this.baseURL + '/updateMarque',marque);
}
deleteAffectation(id: number): Observable<void> {
 
  return this.httpClient.delete<void>(this.baseURL + '/deleteAffectation' + '/' + id);
}


  getAllAffectation(): Observable<AffectationEquipement[]> {
    return this.httpClient.get<AffectationEquipement[]>(`${this.baseURL}/getallAffectation`);
  }



  addModel(model:Model ): Observable<Model> {
    return this.httpClient.post<Model>(`${this.baseURL}/addModel`,model);
  }
  deleteModel(id: number): Observable<void> {
 
  return this.httpClient.delete<void>(this.baseURL + '/deleteModel' + '/' + id);
}
updateModel(model:any): Observable<Object> {
  return this.httpClient.put<Object>(this.baseURL + '/updateModel',model);
}


  deleteEquip(id: number): Observable<void> {
 
  return this.httpClient.delete<void>(this.baseURL + '/deleteEqui' + '/' + id);
}
updateEquip(Equip:any): Observable<Object> {
  return this.httpClient.put<Object>(this.baseURL + '/updateEqui',Equip);
}




  addEquipement(equip:Equip ): Observable<Equip> {
    return this.httpClient.post<Equip>(`${this.baseURL}/addEqui`,equip);
  }

getAllEquipements(page: number, size: number): Observable<any> {
  return this.httpClient.get<any>(`${this.baseURL}/getallEqui?page=${page}&size=${size}`, {
    headers: this.getAuthHeaders()
  });
}

updateFournisseur(Fournisseur:any): Observable<Object> {
  return this.httpClient.put<Object>(this.baseURL + '/updateFournisseur',Fournisseur);
}
  deleteFournisseur(id: number): Observable<void> {
 
  return this.httpClient.delete<void>(this.baseURL + '/deleteFournisseur' + '/' + id);
}
  searchEquipements(keyword: string,username:string, page: number, size: number): Observable<any> {
    let params = new HttpParams()
      .set('keyword', keyword)
.set('username', username)
      .set('page', page)
      .set('size', size);

    return this.httpClient.get<any>(`${this.baseURL}/searchedEqui`, { params });
  }
  searchEquipements1(keyword: string,statut:string, page: number, size: number): Observable<any> {
    let params = new HttpParams()
      .set('keyword', keyword)
.set('statut', statut)


      .set('page', page)
      .set('size', size);

    return this.httpClient.get<any>(`${this.baseURL}/searchedEqui1`, { params });
  }





  addFournisseur(fournisseur:Fournisseur ): Observable<Fournisseur> {
    return this.httpClient.post<Fournisseur>(`${this.baseURL}/addFournisseur`, fournisseur, {
      headers: this.getAuthHeaders()
    });
  }

  getallFournisseur(): Observable<Fournisseur[]> {
    return this.httpClient.get<Fournisseur[]>(`${this.baseURL}/getallFournisseur`, {
      headers: this.getAuthHeaders()
    });
  }




  addAffectaion(affectationequipement:AffectationEquipement ): Observable<AffectationEquipement> {
    return this.httpClient.post<AffectationEquipement>(`${this.baseURL}/affToEqui`,affectationequipement);
  }


  updateAffectation(affectation:any): Observable<Object> {
  return this.httpClient.put<Object>(this.baseURL + '/updateAffectation',affectation);
}



  updateEtat(idAffectation:number,etat:string): Observable<Object> {
    return this.httpClient.put<Object>(this.baseURL + '/updateCommentaire/'+ idAffectation,etat);
  }


  searchUsers(query: string): Observable<any[]> {
    return this.httpClient.get<any[]>(`${this.baseURL}/findedUsers?q=${query}`);
  }
  

  getEquiByI(id:number):Observable<Equip>
  {
return this.httpClient.get<Equip>(`${this.baseURL}/getEquip/${id}`);

  }


  addAff(affectation:Affectation ): Observable<Affectation> {
    return this.httpClient.post<Affectation>(`${this.baseURL}/addAff`,affectation);
  }
addStatutAffecte(id: number): Observable<any> {
  return this.httpClient.put<any>(`${this.baseURL}/statutAffecte/${id}`, {});
}

addStatutDisponible(id: number): Observable<any> {
  return this.httpClient.put<any>(`${this.baseURL}/statutDisponible/${id}`, {});
}

  updateAff(affectation:any): Observable<Object> {
  return this.httpClient.put<Object>(this.baseURL + '/updateAffect',affectation);
}


getAffectationById(id: number): Observable<Affectation> {
  return this.httpClient.get<Affectation>(`${this.baseURL}/getAff/${id}`);
}

deleteAff(id: number): Observable<void> {
  return this.httpClient.delete<void>(`${this.baseURL}/deleteAffectation/${id}`);
}


  addHistorique(historique:Historique ): Observable<Historique> {
    return this.httpClient.post<Historique>(`${this.baseURL}/addHistorique`,historique);
  }
    searchModels(query: string): Observable<any[]> {
    return this.httpClient.get<any[]>(`${this.baseURL}/getMode?q=${query}`);
  }
  getHistoriques(page: number, size: number): Observable<any> {
  return this.httpClient.get<any>(`${this.baseURL}/allHistorique?page=${page}&size=${size}`, {
    headers: this.getAuthHeaders()
  });
}

 searchHistorique(query:string,page:number,size:number): Observable<any> {
let params=new HttpParams()
.set('keyword',query)
.set('page',page)
.set('size',size)
  return this.httpClient.get<any>(`${this.baseURL}/getSearchedHistorique`,{params});
  }


getAffectationsByIds(ids: number[]): Observable<Affectation[]> {
  if (!ids || ids.length === 0)
  {
     console.log('⛔ Aucune ID envoyée à la requête');
    return of([]); // ← évite les requêtes vides
  }
  const params = ids.map(id => `ids=${id}`).join('&'); // "ids=1&ids=2&ids=3"
  return this.httpClient.get<Affectation[]>(`${this.baseURL}/getAffectationsByEquipments?${params}`, {
    headers: this.getAuthHeaders()
  });
}

getDSIEquipements(page: number, size: number): Observable<any> {
  return this.httpClient.get<any>(`${this.baseURL}/DSIEquip?page=${page}&size=${size}`, {
    headers: this.getAuthHeaders()
  });
}


declarerPanne(panneData: any): Observable<any> {
  return this.httpClient.post<any>(`${this.baseURL}/AjoutPanne`, panneData, {
    headers: this.getAuthHeaders()
  });
}

annulerPanne(equipementId: number): Observable<any> {
  return this.httpClient.delete<any>(`${this.baseURL}/DeletePanne/${equipementId}`, {
    headers: this.getAuthHeaders()
  });
}


changerEtatPanne(id: number, responsable: string, nouvleEtat: string): Observable<any> {
  return this.httpClient.put<any>(`${this.baseURL}/changerEtatPanne/${id}?responsable=${responsable}&nouvleEtat=${nouvleEtat}`, {});
}





}
