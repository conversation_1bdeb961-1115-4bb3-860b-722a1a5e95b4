<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="75ef6152-bd1c-4887-b485-5f225598a49c" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/dataSources.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/uiDesigner.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Controller/AuthController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Controller/Rest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Controller/UserManagementController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Controller/imgrest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Affectation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/AffectationEquipement.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Agent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Equipement.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/EtatEqui.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Fournisseur.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Harbor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Historique.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Job.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Marque.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Model.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Panne.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Position.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Status.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/Statut.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/TypeEqui.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/TypeEtat.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/User.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Entity/UserDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/AffectationEquipementRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/AgentRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/EquipRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/EtatRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/FournisseurRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/HarborRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/HstoriqueRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/JobRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/ModelRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/PanneRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/PositionRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/StatusRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/TypeEquiRepo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/User2Repository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/UserRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Repository/UtilisateurClient.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/IServiceEqui.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/ServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/UserService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/auth/CustomUserDetailsService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/auth/JwtAuthenticationFilter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/auth/JwtUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/auth/MailService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/Service/auth/PasswordResetTokenService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/configs/SecurityConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/equip/src/main/java/tn/esprit/equip/configs/WebConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/microserviceUser/src/main/java/com/example/microserviceuser/Entity/Agent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/microservices/microserviceUser/src/main/java/com/example/microserviceuser/Repository/User2Repository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/.gitattributes" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/.mvn/wrapper/maven-wrapper.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/mvnw" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/mvnw.cmd" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/src/main/java/tn/esprit/microserviceconseil/MicroserviceConseilApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/src/main/resources/application.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceConseil/src/test/java/tn/esprit/microserviceconseil/MicroserviceConseilApplicationTests.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/.gitattributes" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/.mvn/wrapper/maven-wrapper.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/mvnw" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/mvnw.cmd" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/src/main/java/tn/esprit/microserviceplanification/MicroservicePlanificationApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/src/main/resources/application.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microservicePlanification/src/test/java/tn/esprit/microserviceplanification/MicroservicePlanificationApplicationTests.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/.gitattributes" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/.mvn/wrapper/maven-wrapper.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/mvnw" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/mvnw.cmd" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/src/main/java/tn/esprit/microservicerapport/MicroserviceRapportApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/src/main/resources/application.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRapport/src/test/java/tn/esprit/microservicerapport/MicroserviceRapportApplicationTests.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/.gitattributes" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/.mvn/wrapper/maven-wrapper.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/mvnw" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/mvnw.cmd" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/src/main/java/tn/esprit/microservicerectification/MicroserviceRectificationApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/src/main/resources/application.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceRectification/src/test/java/tn/esprit/microservicerectification/MicroserviceRectificationApplicationTests.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/microservices/microserviceUser/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/.mvn/wrapper/maven-wrapper.properties" beforeDir="false" afterPath="$PROJECT_DIR$/microservices/microserviceUser/.mvn/wrapper/maven-wrapper.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/mvnw.cmd" beforeDir="false" afterPath="$PROJECT_DIR$/microservices/microserviceUser/mvnw.cmd" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/microservices/microserviceUser/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/src/main/java/tn/esprit/microserviceuser/MicroserviceUserApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/microservices/microserviceUser/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/microservices/microserviceUser/src/test/java/tn/esprit/microserviceuser/MicroserviceUserApplicationTests.java" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Class" />
        <option value="Interface" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/com/netflix/eureka/eureka-client/2.0.4/eureka-client-2.0.4.jar!/com/netflix/discovery/shared/transport/decorator/EurekaHttpClientDecorator.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zPkyZZPUVyT0MqqaHIVBXDtqhC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.EquipApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.MicroserviceConseilApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.MicroserviceUserApplication.executor&quot;: &quot;Run&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/pe/Equipements - Copie (2)&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\Equipements\microservices\equip\src\main\java\tn\esprit\equip\Controller" />
      <recent name="C:\Users\<USER>\Desktop\Equipements\microservices\equip\src\main\java\tn\esprit\equip\Service" />
      <recent name="C:\Users\<USER>\Desktop\Equipements\microservices\equip\src\main\java\tn\esprit\equip\Repository" />
      <recent name="C:\Users\<USER>\Desktop\Equipements\microservices\equip\src\main\java\tn\esprit\equip\Entity" />
      <recent name="C:\Users\<USER>\Desktop\Equipements\microservices\equip\src\main\java\tn\esprit\equip" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="tn.esprit.equip.configs" />
      <recent name="tn.esprit.equip.Service" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.EquipApplication">
    <configuration name="EquipApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="pi" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="tn.esprit.equip.EquipApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MicroserviceUserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="tpFoyer-17" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.microserviceuser.MicroserviceUserApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="75ef6152-bd1c-4887-b485-5f225598a49c" name="Changes" comment="" />
      <created>1751640351405</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751640351405</updated>
      <workItem from="1751640352479" duration="104000" />
      <workItem from="1751640482649" duration="6843000" />
      <workItem from="1751705823670" duration="2771000" />
      <workItem from="1751732099078" duration="3253000" />
      <workItem from="1751798154987" duration="8000" />
      <workItem from="1751798243886" duration="59000" />
      <workItem from="1751798351952" duration="5089000" />
      <workItem from="1751817925950" duration="5578000" />
      <workItem from="1751926755979" duration="2117000" />
      <workItem from="1751980871413" duration="4483000" />
      <workItem from="1752011163560" duration="807000" />
      <workItem from="1752012863106" duration="218000" />
      <workItem from="1752014014371" duration="753000" />
      <workItem from="1752064793212" duration="4257000" />
      <workItem from="1752069777478" duration="1980000" />
      <workItem from="1752072816653" duration="2119000" />
      <workItem from="1752076473020" duration="2469000" />
      <workItem from="1752095956762" duration="2744000" />
      <workItem from="1752100068435" duration="720000" />
      <workItem from="1752162184803" duration="1156000" />
      <workItem from="1752167860126" duration="1583000" />
      <workItem from="1752176793951" duration="4860000" />
      <workItem from="1752182963140" duration="1572000" />
      <workItem from="1752186075603" duration="1534000" />
      <workItem from="1752265783508" duration="1137000" />
      <workItem from="1752309002293" duration="16404000" />
      <workItem from="1752394862698" duration="783000" />
      <workItem from="1752499187416" duration="12000" />
      <workItem from="1752499262533" duration="645000" />
      <workItem from="1752560043495" duration="599000" />
      <workItem from="1752673301625" duration="5499000" />
      <workItem from="1752684508784" duration="2279000" />
      <workItem from="1752739848306" duration="10122000" />
      <workItem from="1752832202475" duration="2177000" />
      <workItem from="1752843682678" duration="11000" />
      <workItem from="1752851108943" duration="2450000" />
      <workItem from="1753017942008" duration="11571000" />
      <workItem from="1753077425291" duration="363000" />
      <workItem from="1753181382264" duration="14925000" />
      <workItem from="1753258483725" duration="599000" />
      <workItem from="1753284093649" duration="601000" />
      <workItem from="1753285630801" duration="7897000" />
      <workItem from="1753304181037" duration="1000000" />
      <workItem from="1753306108043" duration="2728000" />
      <workItem from="1753335465811" duration="3657000" />
      <workItem from="1753351532320" duration="3478000" />
      <workItem from="1753364910749" duration="8736000" />
      <workItem from="1753438448927" duration="15782000" />
      <workItem from="1753480911039" duration="940000" />
      <workItem from="1753513324910" duration="661000" />
      <workItem from="1753542936605" duration="1339000" />
      <workItem from="1753552416794" duration="7848000" />
      <workItem from="1753602705383" duration="17000" />
      <workItem from="1753629430226" duration="7682000" />
      <workItem from="1753656960686" duration="2109000" />
      <workItem from="1753684938874" duration="6829000" />
      <workItem from="1753789047392" duration="1166000" />
      <workItem from="1753790688096" duration="896000" />
      <workItem from="1753806479017" duration="3615000" />
      <workItem from="1753816781698" duration="158000" />
      <workItem from="1753874637276" duration="60000" />
      <workItem from="1753892033819" duration="5257000" />
      <workItem from="1753944672977" duration="11000" />
      <workItem from="1753949128105" duration="34000" />
      <workItem from="1753989186788" duration="5970000" />
      <workItem from="1754023034967" duration="2961000" />
      <workItem from="1754042744289" duration="1258000" />
      <workItem from="1754064461836" duration="3420000" />
      <workItem from="1754082544923" duration="3473000" />
      <workItem from="1754118583134" duration="2752000" />
      <workItem from="1754133294432" duration="375000" />
      <workItem from="1754133689872" duration="12205000" />
      <workItem from="1754154145204" duration="973000" />
      <workItem from="1754157894910" duration="7271000" />
      <workItem from="1754214808625" duration="16636000" />
      <workItem from="1754292045718" duration="1850000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>