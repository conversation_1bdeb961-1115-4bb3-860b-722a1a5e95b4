{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Equip } from 'src/app/equipement/equip';\nimport { FormControl, Validators } from '@angular/forms';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Panne } from './Panne';\nexport let EquipementsComponent = class EquipementsComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null,\n      panne: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null,\n      panne: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    // Propriétés pour les pannes\n    this.selectedPanne = new Panne();\n    this.showPanneModal = false;\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.signupErrors = {};\n    // Initialisation du formulaire de panne\n    this.panneForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: ['', [Validators.required, Validators.minLength(10)]],\n      priorite: ['MOYENNE', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    equiements.forEach(eq => {\n      this.idsEqui[eq.idEqui] = eq.idEqui;\n    });\n    console.log(this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire=\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  openPanneModal(equipement) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n  /**\n   * Fermer le modal de panne\n   */\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n  /**\n   * Fermer le modal en cliquant à l'extérieur\n   */\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closePanneModal();\n    }\n  }\n  onSubmitPanne() {\n    if (this.panneForm.valid) {\n      const Etat = {\n        titre: 'PANNE',\n        responsable: \"DAG\",\n        precedent: null\n      };\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement,\n        etatActuel: Etat\n      };\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: response => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          this.loadEquipements(this.currentPage);\n        },\n        error: error => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n        }\n      });\n    }\n  }\n  getEquipementEtat(equip) {\n    if (!equip.panne) {\n      return 'EN MARCHE';\n    }\n    return equip.panne.etatActuel?.titre || 'INCONNU';\n  }\n  annulerPanne(equipement) {\n    if (confirm('Êtes-vous sûr de vouloir annuler cette panne ?')) {\n      // Appel API pour supprimer la panne\n      this.authservice.annulerPanne(equipement.idEqui).subscribe({\n        next: response => {\n          console.log('Panne annulée avec succès:', response);\n          this.showNotification('success', 'Panne annulée avec succès');\n          this.loadEquipements(this.currentPage);\n        },\n        error: error => {\n          console.error('Erreur lors de l\\'annulation de la panne:', error);\n          this.showNotification('error', 'Erreur lors de l\\'annulation de la panne');\n        }\n      });\n    }\n  }\n};\nEquipementsComponent = __decorate([Component({\n  selector: 'app-equipements',\n  templateUrl: './equipements.component.html',\n  styleUrls: ['./equipements.component.css']\n})], EquipementsComponent);", "map": {"version": 3, "names": ["Component", "Equip", "FormControl", "Validators", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "<PERSON><PERSON>", "EquipementsComponent", "constructor", "authservice", "http", "fb", "utilisateurService", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "notification", "show", "type", "message", "currentPage", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "idEqui", "numSerie", "statut", "image", "model", "dateAffectation", "Date", "description", "<PERSON><PERSON><PERSON><PERSON>", "panne", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "NameUtilisateur", "idsEqui", "tableAffectation", "<PERSON><PERSON><PERSON>", "showPanneModal", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "signupErrors", "panneForm", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "priorite", "ngOnInit", "GetAllModels", "loadEquipements", "getFournisseur", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "totalPages", "fetchUtilisateurs", "error", "err", "console", "searchUsers", "users", "displayUtilisateur", "firstName", "lastName", "email", "displayModel", "nomModel", "getallFournisseur", "data", "onUserSearchSelected", "page", "keyword", "username", "userVal", "searchEquipements1", "getDSIEquipements", "log", "for<PERSON>ach", "eq", "getAffectationsByIds", "affectation", "onSearchChange", "onFileSelected", "event", "file", "target", "files", "formData", "FormData", "append", "post", "response", "imageUrl", "fullUrl", "form", "patchValue", "resetErrors", "getAllModel", "showNotification", "setTimeout", "hideNotification", "formatDateForInput", "date", "date<PERSON><PERSON>j", "isNaN", "getTime", "toISOString", "split", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "openPanneModal", "reset", "closePanneModal", "closeOnOutsideClick", "classList", "contains", "onSubmitPanne", "valid", "Etat", "responsable", "precedent", "panneData", "get", "etatActuel", "declarer<PERSON><PERSON>", "getEquipementEtat", "equip", "annuler<PERSON>anne", "confirm", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.ts"], "sourcesContent": ["\nimport { Component, OnInit } from '@angular/core';\nimport { Equip } from 'src/app/equipement/equip';\nimport { Model } from 'src/app/model/Model';\nimport { TypeService } from 'src/app/dashboard/type.service'; \nimport { HttpClient } from '@angular/common/http';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\n// or for just Modal:\nimport { Modal } from 'bootstrap';\nimport { Fournisseur } from 'src/app/fournisseur/Fournisseur';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { UtilisateurService } from 'src/app/utilisateur/utilisateur.service';\n\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Affectation } from 'src/app/affecta/Affectation';\nimport { Historique } from 'src/app/equipement/Historique';\nimport { Panne } from './Panne';\nimport { EtatEqui } from './EtatEqui';\n@Component({\n  selector: 'app-equipements',\n  templateUrl: './equipements.component.html',\n  styleUrls: ['./equipements.component.css']\n})\n\n\n\nexport class EquipementsComponent implements OnInit {\n\n\n\nmodels:Model[]=[];\nequiements:Equip[]=[];\nutilisateurs: Utilisateur[] = [];\n\nfilteredUtilisateurs: Utilisateur[] = [];\nfilteredUtilisateursSearch: Utilisateur[] = [];\nmodelet: Model[] = [];\n  NomEqui:String|null=null;\n    NomUser:String|null=null;\nnotification = {\n  show: false,\n  type: 'success', // 'success' or 'error'\n  message: ''\n};\ncurrentPage = 0;\npageSize = 4;\nsearchTerm: string = '';\n\n// Affectation form\naffectationForm!: FormGroup;\nselectedEquipement!: Equip \naffectationFormSubmitted = false;\neditAffectationFormSubmitted = false;\n\nnewEquipement:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\npanne:null\n\n};\nnewEquipement1:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\npanne:null\n\n};\nform!: FormGroup;\neditForm!: FormGroup;\n\nEditedAffectation:Affectation={\n  id:0,\n  commentaire:\"\",\n  dateAffectation:new Date(),\n  user:new Utilisateur(),\n  equipement:new Equip(),\n  verrou:\"\"\n\n}\nNameUtilisateur:string[]=[];\nidsEqui:number[]=[];\ntableAffectation: any = {};\n\n// Propriétés pour les pannes\nselectedPanne: Panne = new Panne();\nshowPanneModal: boolean = false;\npanneForm: FormGroup;\n\nsubmitted = false;\nfournisseurs:Fournisseur[]=[]; \n  totalPages: any;\n  utilisateurCtrl = new FormControl();\n  utilisateurSearchCtrl = new FormControl();\n  modelCtrl = new FormControl();\nconstructor(\n  private authservice:TypeService,\n  private http:HttpClient,\n  private fb: FormBuilder,\n  private utilisateurService: UtilisateurService\n) {\n  // Initialisation du formulaire de panne\n  this.panneForm = this.fb.group({\n    titre: ['', [Validators.required, Validators.minLength(3)]],\n    description: ['', [Validators.required, Validators.minLength(10)]],\n    priorite: ['MOYENNE', [Validators.required]],\n\n  });\n}\n  ngOnInit(): void {\n      this.currentPage = 0;\n\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n \n\n\n\n\n\n\n\n\n// Autocomplete pour le modal de modification\nthis.modelCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n// Autocomplete pour le formulaire d'ajout\n\n\n\n// Autocomplete pour la recherche\nthis.utilisateurSearchCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    tap((value: any) => {\n\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\n        this.loadEquipements(0);\n      }\n    }),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\n  next: (res) => {\n    this.equiements = res.content;\n    this.totalPages = res.totalPages;\n    this.fetchUtilisateurs(this.equiements);\n  },\n  error: (err) => console.error(err)\n});\nthis.loadEquipements(0);\n          \n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(users => {\n    this.filteredUtilisateursSearch = users;\n  });\n\n\n\n}\n\n\n\n\n  \n\ndisplayUtilisateur(user: any): string {\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n}\n\n\ndisplayModel(model: any): string {\n  return  model? `${model.nomModel}` : '';\n}\n\n\n\n\n\n getFournisseur()\n  {\n\n  this.authservice.getallFournisseur().subscribe(data => {\n  this.fournisseurs = data;\n\n});\n\n\n  }\n\n\n\n\n\n\n\n\n\n \n\n  onUserSearchSelected(user: Utilisateur) {\n    this.loadEquipements(0);\n \n  }\n\n\n\n\n\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\n\nloadEquipements(page: number): void {\n  this.currentPage = page;\n\n  const keyword = this.searchTerm.trim();\n  const statut = this.selectedStatut.trim();\n\n  let username = '';\n  const userVal = this.utilisateurSearchCtrl.value;\n\n  if (typeof userVal === 'string') {\n    username = userVal.trim();\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n    username = userVal.username.trim();\n  }\n\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n  if (username !== '') {\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 2 : Statut seul (sans username)\n  if (keyword === '' && statut !== '') {\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 3 : Keyword seul (sans username ni statut)\n  if (keyword !== '' && statut === '') {\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 4 : Keyword + Statut (sans username)\n  if (keyword !== '' && statut !== '') {\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 5 : Aucun filtre → tout afficher\n  this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n    next: (res) => {\n      this.equiements = res.content;\n      this.totalPages = res.totalPages;\n      this.fetchUtilisateurs(this.equiements);\n    },\n    error: (err) => console.error(err)\n  });\n}\n\n\nprivate fetchUtilisateurs(equiements: any[]): void {\n  console.log(equiements);\n  equiements.forEach(eq => {\n   \n    this.idsEqui[eq.idEqui]=eq.idEqui;\n     })\n     console.log(this.idsEqui); \n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      \n      });\n    });\n\n\n\n\n}\n\n\n  onSearchChange(): void {\n\n    this.loadEquipements(0);\n  }\n\n  \n  \n\n\n\n\n\n   \n\n\n\n\n\n\n\n    \n\n\n\n\n  \nonFileSelected(event: any) {\n  const file = event.target.files[0];\n\n  if (file) {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\n      (response) => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n\n         \n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      },\n      (error) => {\n        console.error('Error during image upload', error);\n      }\n    );\n  }\n}\n\n\n\nsignupErrors: any = {};\n    \n  resetErrors() {\n    this.signupErrors = {};\n  }\n\n      GetAllModels()\n    {\n      this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n   \n    });\n    }\n\n\n\n\n \n  // Méthodes pour les notifications\n  showNotification(type: 'success' | 'error', message: string) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n\n  hideNotification() {\n    this.notification.show = false;\n  }\n\n  // Méthode pour réinitialiser le formulaire=\n\n\n\n\n\n\n\n\n  \n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date: any): string | null {\n    if (!date) return null;\n\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n\n  // Méthodes pour la pagination\n  goToPage(page: number): void {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n\n  // Méthode pour générer les numéros de pages\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n\n  openPanneModal(equipement: Equip) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n\n  /**\n   * Fermer le modal de panne\n   */\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n\n  /**\n   * Fermer le modal en cliquant à l'extérieur\n   */\n  closeOnOutsideClick(event: any) {\n    if (event.target.classList.contains('modal')) {\n      this.closePanneModal();\n    }\n  }\n\n  onSubmitPanne() {\n\n\n    if (this.panneForm.valid) {\n      const Etat={\ntitre:'PANNE',\nresponsable:\"DAG\",\nprecedent:null\n\n\n};\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement,\n        etatActuel:Etat\n      };\n\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: (response: any) => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          this.loadEquipements(this.currentPage);\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n        }\n      });\n    }\n  }\n\n \n  getEquipementEtat(equip: any): string {\n    if (!equip.panne) {\n      return 'EN MARCHE';\n    }\n    return equip.panne.etatActuel?.titre || 'INCONNU';\n  }\n\n  annulerPanne(equipement: Equip) {\n    if (confirm('Êtes-vous sûr de vouloir annuler cette panne ?')) {\n      // Appel API pour supprimer la panne\n      this.authservice.annulerPanne(equipement.idEqui).subscribe({\n        next: (response: any) => {\n          console.log('Panne annulée avec succès:', response);\n          this.showNotification('success', 'Panne annulée avec succès');\n          this.loadEquipements(this.currentPage);\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de l\\'annulation de la panne:', error);\n          this.showNotification('error', 'Erreur lors de l\\'annulation de la panne');\n        }\n      });\n    }\n  }\n\n}\n\n  \n\n\n\n"], "mappings": ";AACA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,KAAK,QAAQ,0BAA0B;AAIhD,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,iCAAiC;AAG7D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAG7E,SAASC,KAAK,QAAQ,SAAS;AAUxB,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EA+EjCC,YACUC,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA/E5B,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAAC,YAAY,GAAG;MACbC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE;KACV;IACD,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBC,MAAM,EAAC,CAAC;MACRC,QAAQ,EAAC,EAAE;MACXC,MAAM,EAAC,EAAE;MACTC,KAAK,EAAC,EAAE;MACRC,KAAK,EAAC,IAAI;MACVC,eAAe,EAAC,IAAIC,IAAI,CAAJ,CAAI;MACxBC,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC,IAAI;MAChBC,KAAK,EAAC;KAEL;IACD,KAAAC,cAAc,GAAO;MACrBV,MAAM,EAAC,CAAC;MACRC,QAAQ,EAAC,EAAE;MACXC,MAAM,EAAC,EAAE;MACTC,KAAK,EAAC,EAAE;MACRC,KAAK,EAAC,IAAI;MACVC,eAAe,EAAC,IAAIC,IAAI,CAAJ,CAAI;MACxBC,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC,IAAI;MAChBC,KAAK,EAAC;KAEL;IAID,KAAAE,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdR,eAAe,EAAC,IAAIC,IAAI,EAAE;MAC1BQ,IAAI,EAAC,IAAI7C,WAAW,EAAE;MACtB8C,UAAU,EAAC,IAAIjD,KAAK,EAAE;MACtBkD,MAAM,EAAC;KAER;IACD,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B;IACA,KAAAC,aAAa,GAAU,IAAI7C,KAAK,EAAE;IAClC,KAAA8C,cAAc,GAAY,KAAK;IAG/B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAIzD,WAAW,EAAE;IACnC,KAAA0D,qBAAqB,GAAG,IAAI1D,WAAW,EAAE;IACzC,KAAA2D,SAAS,GAAG,IAAI3D,WAAW,EAAE;IAgJ/B,KAAA4D,cAAc,GAAW,EAAE,CAAC,CAAC;IA+J7B,KAAAC,YAAY,GAAQ,EAAE;IAxSpB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACjD,EAAE,CAACkD,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC/D,UAAU,CAACgE,QAAQ,EAAEhE,UAAU,CAACiE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D1B,WAAW,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAACgE,QAAQ,EAAEhE,UAAU,CAACiE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAClEC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAClE,UAAU,CAACgE,QAAQ,CAAC;KAE5C,CAAC;EACJ;EACEG,QAAQA,CAAA;IACJ,IAAI,CAACzC,WAAW,GAAG,CAAC;IAEtB,IAAI,CAAC0C,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC3C,WAAW,CAAC;IACtC,IAAI,CAAC4C,cAAc,EAAE;IAUzB;IACA,IAAI,CAACZ,SAAS,CAACa,YAAY,CACxBC,IAAI,CACHtE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACoE,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACjE,WAAW,CAACkE,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAOtE,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAyE,SAAS,CAAC/D,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IAIA;IACA,IAAI,CAAC2C,qBAAqB,CAACc,YAAY,CACpCC,IAAI,CACHtE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAEmE,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAACjB,qBAAqB,CAACqB,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAACV,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACFhE,SAAS,CAACoE,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAACjE,WAAW,CAACsE,iBAAiB,CAAC,IAAI,CAACpD,UAAU,EAAC,IAAI,CAAC6B,qBAAqB,CAACgB,KAAK,EAAC,CAAC,EAAC,IAAI,CAAC9C,QAAQ,CAAC,CAACkD,SAAS,CAAC;YAC7GI,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAACnE,UAAU,GAAGmE,GAAG,CAACC,OAAO;cAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;cAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACtE,UAAU,CAAC;YACzC,CAAC;YACDuE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAAClB,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAAC3D,WAAW,CAAC+E,WAAW,CAAChB,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAOtE,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAyE,SAAS,CAACa,KAAK,IAAG;MACjB,IAAI,CAACxE,0BAA0B,GAAGwE,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAC,kBAAkBA,CAAC7C,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAAC8C,SAAS,IAAI9C,IAAI,CAAC+C,QAAQ,MAAM/C,IAAI,CAACgD,KAAK,EAAE,GAAG,EAAE;EACzE;EAGAC,YAAYA,CAAC3D,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAAC4D,QAAQ,EAAE,GAAG,EAAE;EACzC;EAMC1B,cAAcA,CAAA;IAGb,IAAI,CAAC5D,WAAW,CAACuF,iBAAiB,EAAE,CAACpB,SAAS,CAACqB,IAAI,IAAG;MACtD,IAAI,CAAC3C,YAAY,GAAG2C,IAAI;IAE1B,CAAC,CAAC;EAGA;EAYAC,oBAAoBA,CAACrD,IAAiB;IACpC,IAAI,CAACuB,eAAe,CAAC,CAAC,CAAC;EAEzB;EAQFA,eAAeA,CAAC+B,IAAY;IAC1B,IAAI,CAAC1E,WAAW,GAAG0E,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAACzE,UAAU,CAAC8C,IAAI,EAAE;IACtC,MAAMxC,MAAM,GAAG,IAAI,CAACyB,cAAc,CAACe,IAAI,EAAE;IAEzC,IAAI4B,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAAC9C,qBAAqB,CAACgB,KAAK;IAEhD,IAAI,OAAO8B,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAAC7B,IAAI,EAAE;KAC1B,MAAM,IAAI6B,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAAC5B,IAAI,EAAE;;IAGpC;IACA,IAAI4B,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAC5F,WAAW,CAACsE,iBAAiB,CAACqB,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAACzE,QAAQ,CAAC,CAACkD,SAAS,CAAC;QACzFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACnE,UAAU,GAAGmE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACtE,UAAU,CAAC;QACzC,CAAC;QACDuE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIc,OAAO,KAAK,EAAE,IAAInE,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACxB,WAAW,CAAC8F,kBAAkB,CAAC,EAAE,EAAEtE,MAAM,EAAEkE,IAAI,EAAE,IAAI,CAACzE,QAAQ,CAAC,CAACkD,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACnE,UAAU,GAAGmE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACtE,UAAU,CAAC;QACzC,CAAC;QACDuE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIc,OAAO,KAAK,EAAE,IAAInE,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACxB,WAAW,CAACsE,iBAAiB,CAACqB,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAACzE,QAAQ,CAAC,CAACkD,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACnE,UAAU,GAAGmE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACtE,UAAU,CAAC;QACzC,CAAC;QACDuE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIc,OAAO,KAAK,EAAE,IAAInE,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACxB,WAAW,CAAC8F,kBAAkB,CAACH,OAAO,EAAEnE,MAAM,EAAEkE,IAAI,EAAE,IAAI,CAACzE,QAAQ,CAAC,CAACkD,SAAS,CAAC;QAClFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACnE,UAAU,GAAGmE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACtE,UAAU,CAAC;QACzC,CAAC;QACDuE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAAC7E,WAAW,CAAC+F,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAACzE,QAAQ,CAAC,CAACkD,SAAS,CAAC;MAChEI,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACnE,UAAU,GAAGmE,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;QAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACtE,UAAU,CAAC;MACzC,CAAC;MACDuE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAACtE,UAAiB;IACzCyE,OAAO,CAACkB,GAAG,CAAC3F,UAAU,CAAC;IACvBA,UAAU,CAAC4F,OAAO,CAACC,EAAE,IAAG;MAEtB,IAAI,CAAC1D,OAAO,CAAC0D,EAAE,CAAC5E,MAAM,CAAC,GAAC4E,EAAE,CAAC5E,MAAM;IAChC,CAAC,CAAC;IACFwD,OAAO,CAACkB,GAAG,CAAC,IAAI,CAACxD,OAAO,CAAC;IAC1B,IAAI,CAACxC,WAAW,CAACmG,oBAAoB,CAAC,IAAI,CAAC3D,OAAO,CAAC,CAAC2B,SAAS,CAACqB,IAAI,IAAG;MAEnEA,IAAI,CAACS,OAAO,CAACG,WAAW,IAAG;QACzB,IAAI,CAAC3D,gBAAgB,CAAC2D,WAAW,CAAC/D,UAAU,CAACf,MAAM,CAAC,GAAG8E,WAAW;QAClE,IAAI,CAAC7D,eAAe,CAAC6D,WAAW,CAAC/D,UAAU,CAACf,MAAM,CAAC,GAAG8E,WAAW,CAAChE,IAAI,CAAC8C,SAAS,GAAG,GAAG,GAAGkB,WAAW,CAAChE,IAAI,CAAC+C,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGEkB,cAAcA,CAAA;IAEZ,IAAI,CAAC1C,eAAe,CAAC,CAAC,CAAC;EACzB;EAuBF2C,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAE7B,IAAI,CAACvG,IAAI,CAAC6G,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACxC,SAAS,CACpE4C,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBF,QAAQ,CAACC,QAAQ,EAAE;UAC3DlC,OAAO,CAACkB,GAAG,CAAC,mBAAmB,EAAEiB,OAAO,CAAC;UAGzC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC;YACnB1F,KAAK,EAAEwF;WACR,CAAC;SACH,MAAM;UACLnC,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAMEwC,WAAWA,CAAA;IACT,IAAI,CAAClE,YAAY,GAAG,EAAE;EACxB;EAEIQ,YAAYA,CAAA;IAEZ,IAAI,CAAC1D,WAAW,CAACqH,WAAW,EAAE,CAAClD,SAAS,CAACqB,IAAI,IAAG;MAChD,IAAI,CAACpF,MAAM,GAAGoF,IAAI;IAEpB,CAAC,CAAC;EACF;EAMF;EACA8B,gBAAgBA,CAACxG,IAAyB,EAAEC,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClBC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA;KACV;IAED;IACAwG,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC5G,YAAY,CAACC,IAAI,GAAG,KAAK;EAChC;EAEA;EAUA;EACA4G,kBAAkBA,CAACC,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAMC,OAAO,GAAG,IAAI/F,IAAI,CAAC8F,IAAI,CAAC;MAC9B,IAAIE,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAOnD,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACAoD,QAAQA,CAACtC,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAAChB,UAAU,EAAE;MACvC,IAAI,CAACf,eAAe,CAAC+B,IAAI,CAAC;;EAE9B;EAEAuC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjH,WAAW,GAAG,IAAI,CAAC0D,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACf,eAAe,CAAC,IAAI,CAAC3C,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEAkH,YAAYA,CAAA;IACV,IAAI,IAAI,CAAClH,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC2C,eAAe,CAAC,IAAI,CAAC3C,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACAmH,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxH,WAAW,GAAGuH,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAACjE,UAAU,GAAG,CAAC,EAAE4D,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;EAGAU,cAAcA,CAACzG,UAAiB;IAC9B,IAAI,CAACK,aAAa,GAAG,IAAI7C,KAAK,EAAE;IAChC,IAAI,CAAC6C,aAAa,CAACL,UAAU,GAAGA,UAAU;IAC1C,IAAI,CAACc,SAAS,CAAC4F,KAAK,EAAE;IACtB,IAAI,CAAC5F,SAAS,CAACgE,UAAU,CAAC;MACxB3D,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAACb,cAAc,GAAG,IAAI;EAC5B;EAEA;;;EAGAqG,eAAeA,CAAA;IACb,IAAI,CAACrG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACD,aAAa,GAAG,IAAI7C,KAAK,EAAE;IAChC,IAAI,CAACsD,SAAS,CAAC4F,KAAK,EAAE;EACxB;EAEA;;;EAGAE,mBAAmBA,CAAC1C,KAAU;IAC5B,IAAIA,KAAK,CAACE,MAAM,CAACyC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC5C,IAAI,CAACH,eAAe,EAAE;;EAE1B;EAEAI,aAAaA,CAAA;IAGX,IAAI,IAAI,CAACjG,SAAS,CAACkG,KAAK,EAAE;MACxB,MAAMC,IAAI,GAAC;QACjBjG,KAAK,EAAC,OAAO;QACbkG,WAAW,EAAC,KAAK;QACjBC,SAAS,EAAC;OAGT;MACK,MAAMC,SAAS,GAAG;QAChBpG,KAAK,EAAE,IAAI,CAACF,SAAS,CAACuG,GAAG,CAAC,OAAO,CAAC,EAAE3F,KAAK;QACzClC,WAAW,EAAE,IAAI,CAACsB,SAAS,CAACuG,GAAG,CAAC,aAAa,CAAC,EAAE3F,KAAK;QACrDP,QAAQ,EAAE,IAAI,CAACL,SAAS,CAACuG,GAAG,CAAC,UAAU,CAAC,EAAE3F,KAAK;QAC/C1B,UAAU,EAAE,IAAI,CAACK,aAAa,CAACL,UAAU;QACzCsH,UAAU,EAACL;OACZ;MAED,IAAI,CAACtJ,WAAW,CAAC4J,aAAa,CAACH,SAAS,CAAC,CAACtF,SAAS,CAAC;QAClDI,IAAI,EAAGwC,QAAa,IAAI;UACtBjC,OAAO,CAACkB,GAAG,CAAC,6BAA6B,EAAEe,QAAQ,CAAC;UACpD,IAAI,CAACiC,eAAe,EAAE;UACtB,IAAI,CAACrF,eAAe,CAAC,IAAI,CAAC3C,WAAW,CAAC;QACxC,CAAC;QACD4D,KAAK,EAAGA,KAAU,IAAI;UACpBE,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QACjE;OACD,CAAC;;EAEN;EAGAiF,iBAAiBA,CAACC,KAAU;IAC1B,IAAI,CAACA,KAAK,CAAC/H,KAAK,EAAE;MAChB,OAAO,WAAW;;IAEpB,OAAO+H,KAAK,CAAC/H,KAAK,CAAC4H,UAAU,EAAEtG,KAAK,IAAI,SAAS;EACnD;EAEA0G,YAAYA,CAAC1H,UAAiB;IAC5B,IAAI2H,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D;MACA,IAAI,CAAChK,WAAW,CAAC+J,YAAY,CAAC1H,UAAU,CAACf,MAAM,CAAC,CAAC6C,SAAS,CAAC;QACzDI,IAAI,EAAGwC,QAAa,IAAI;UACtBjC,OAAO,CAACkB,GAAG,CAAC,4BAA4B,EAAEe,QAAQ,CAAC;UACnD,IAAI,CAACO,gBAAgB,CAAC,SAAS,EAAE,2BAA2B,CAAC;UAC7D,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAAC3C,WAAW,CAAC;QACxC,CAAC;QACD4D,KAAK,EAAGA,KAAU,IAAI;UACpBE,OAAO,CAACF,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UACjE,IAAI,CAAC0C,gBAAgB,CAAC,OAAO,EAAE,0CAA0C,CAAC;QAC5E;OACD,CAAC;;EAEN;CAED;AAxjBYxH,oBAAoB,GAAAmK,UAAA,EARhC9K,SAAS,CAAC;EACT+K,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,6BAA6B;CAC1C,CAAC,C,EAIWtK,oBAAoB,CAwjBhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}